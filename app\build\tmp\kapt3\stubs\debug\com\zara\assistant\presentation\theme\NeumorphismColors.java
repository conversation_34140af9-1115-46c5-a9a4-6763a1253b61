package com.zara.assistant.presentation.theme;

import androidx.compose.material3.*;
import androidx.compose.runtime.Composable;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0018\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0019\u0010\u0003\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006R\u0019\u0010\b\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\t\u0010\u0006R\u0019\u0010\n\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000b\u0010\u0006R\u0019\u0010\f\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\r\u0010\u0006R\u0019\u0010\u000e\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000f\u0010\u0006R\u0019\u0010\u0010\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0011\u0010\u0006R\u0019\u0010\u0012\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0013\u0010\u0006R\u0019\u0010\u0014\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0015\u0010\u0006R\u0019\u0010\u0016\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0017\u0010\u0006R\u0019\u0010\u0018\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0019\u0010\u0006R\u0019\u0010\u001a\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001b\u0010\u0006\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006\u001c"}, d2 = {"Lcom/zara/assistant/presentation/theme/NeumorphismColors;", "", "()V", "CoralGlow", "Landroidx/compose/ui/graphics/Color;", "getCoralGlow-0d7_KjU", "()J", "J", "DarkBase", "getDarkBase-0d7_KjU", "DarkHighlight", "getDarkHighlight-0d7_KjU", "DarkShadowDark", "getDarkShadowDark-0d7_KjU", "DarkShadowLight", "getDarkShadowLight-0d7_KjU", "LightBase", "getLightBase-0d7_KjU", "LightHighlight", "getLightHighlight-0d7_KjU", "LightShadowDark", "getLightShadowDark-0d7_KjU", "LightShadowLight", "getLightShadowLight-0d7_KjU", "PurpleGlow", "getPurpleGlow-0d7_KjU", "TealGlow", "getTealGlow-0d7_KjU", "app_debug"})
public final class NeumorphismColors {
    private static final long DarkShadowLight = 0L;
    private static final long DarkShadowDark = 0L;
    private static final long DarkHighlight = 0L;
    private static final long DarkBase = 0L;
    private static final long LightShadowLight = 0L;
    private static final long LightShadowDark = 0L;
    private static final long LightHighlight = 0L;
    private static final long LightBase = 0L;
    private static final long PurpleGlow = 0L;
    private static final long TealGlow = 0L;
    private static final long CoralGlow = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.presentation.theme.NeumorphismColors INSTANCE = null;
    
    private NeumorphismColors() {
        super();
    }
}