package com.zara.assistant.data.local.dao;

import androidx.room.*;
import com.zara.assistant.data.local.entity.ConversationEntity;
import com.zara.assistant.domain.model.Emotion;
import kotlinx.coroutines.flow.Flow;
import java.util.Date;

/**
 * 💬 Conversation DAO - God Mode Database Access
 *
 * Features:
 * - Reactive Flow queries
 * - Complex search capabilities
 * - Performance optimized
 * - Analytics support
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\n\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u000e\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0004J(\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00120\u00112\b\b\u0002\u0010\u0013\u001a\u00020\n2\b\b\u0002\u0010\u0014\u001a\u00020\nH\'J$\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00120\u00112\u0006\u0010\u0016\u001a\u00020\f2\u0006\u0010\u0017\u001a\u00020\fH\'J\u001c\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00120\u00112\u0006\u0010\u0019\u001a\u00020\u001aH\'J\u001c\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00120\u00112\u0006\u0010\u001c\u001a\u00020\u001dH\'J\u001c\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001f0\u00122\u0006\u0010\u0016\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0014\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00120\u0011H\'J\u001e\u0010!\u001a\b\u0012\u0004\u0012\u00020\"0\u00122\b\b\u0002\u0010\u0013\u001a\u00020\nH\u00a7@\u00a2\u0006\u0002\u0010#J\u0016\u0010$\u001a\u00020%2\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001e\u0010&\u001a\u00020\u00032\u0006\u0010\'\u001a\u00020%2\u0006\u0010(\u001a\u00020\nH\u00a7@\u00a2\u0006\u0002\u0010)J&\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00120\u00112\u0006\u0010+\u001a\u00020\u001d2\b\b\u0002\u0010\u0013\u001a\u00020\nH\'J\u0016\u0010,\u001a\u00020\u00032\u0006\u0010\'\u001a\u00020%H\u00a7@\u00a2\u0006\u0002\u0010-J\u0016\u0010.\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006/"}, d2 = {"Lcom/zara/assistant/data/local/dao/ConversationDao;", "", "clearAllConversations", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteConversation", "conversation", "Lcom/zara/assistant/data/local/entity/ConversationEntity;", "(Lcom/zara/assistant/data/local/entity/ConversationEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldConversations", "", "cutoffDate", "Ljava/util/Date;", "(Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getConversationStats", "Lcom/zara/assistant/data/local/dao/ConversationStats;", "getConversations", "Lkotlinx/coroutines/flow/Flow;", "", "limit", "offset", "getConversationsByDateRange", "startDate", "endDate", "getConversationsByEmotion", "emotion", "Lcom/zara/assistant/domain/model/Emotion;", "getConversationsByProvider", "provider", "", "getDailyConversationCounts", "Lcom/zara/assistant/data/local/dao/DailyCount;", "getFavoriteConversations", "getMostCommonTopics", "Lcom/zara/assistant/data/local/dao/TopicFrequency;", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertConversation", "", "rateConversation", "id", "rating", "(JILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchConversations", "query", "toggleFavorite", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateConversation", "app_debug"})
@androidx.room.Dao()
public abstract interface ConversationDao {
    
    /**
     * 📚 Get all conversations with pagination
     */
    @androidx.room.Query(value = "SELECT * FROM conversations ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.ConversationEntity>> getConversations(int limit, int offset);
    
    /**
     * 🔍 Search conversations by text
     */
    @androidx.room.Query(value = "\n        SELECT * FROM conversations \n        WHERE user_input LIKE \'%\' || :query || \'%\' \n        OR zara_response LIKE \'%\' || :query || \'%\'\n        ORDER BY created_at DESC\n        LIMIT :limit\n    ")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.ConversationEntity>> searchConversations(@org.jetbrains.annotations.NotNull()
    java.lang.String query, int limit);
    
    /**
     * 📅 Get conversations by date range
     */
    @androidx.room.Query(value = "\n        SELECT * FROM conversations \n        WHERE created_at BETWEEN :startDate AND :endDate\n        ORDER BY created_at DESC\n    ")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.ConversationEntity>> getConversationsByDateRange(@org.jetbrains.annotations.NotNull()
    java.util.Date startDate, @org.jetbrains.annotations.NotNull()
    java.util.Date endDate);
    
    /**
     * 😊 Get conversations by emotion
     */
    @androidx.room.Query(value = "SELECT * FROM conversations WHERE emotion = :emotion ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.ConversationEntity>> getConversationsByEmotion(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Emotion emotion);
    
    /**
     * 🤖 Get conversations by AI provider
     */
    @androidx.room.Query(value = "SELECT * FROM conversations WHERE ai_provider = :provider ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.ConversationEntity>> getConversationsByProvider(@org.jetbrains.annotations.NotNull()
    java.lang.String provider);
    
    /**
     * ⭐ Get favorite conversations
     */
    @androidx.room.Query(value = "SELECT * FROM conversations WHERE is_favorite = 1 ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.ConversationEntity>> getFavoriteConversations();
    
    /**
     * 📊 Get conversation statistics
     */
    @androidx.room.Query(value = "\n        SELECT \n            COUNT(*) as total_count,\n            AVG(confidence_score) as avg_confidence,\n            AVG(response_time_ms) as avg_response_time,\n            COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_count\n        FROM conversations\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getConversationStats(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.data.local.dao.ConversationStats> $completion);
    
    /**
     * 📈 Get daily conversation counts
     */
    @androidx.room.Query(value = "\n        SELECT DATE(created_at/1000, \'unixepoch\') as date, COUNT(*) as count\n        FROM conversations \n        WHERE created_at >= :startDate\n        GROUP BY DATE(created_at/1000, \'unixepoch\')\n        ORDER BY date DESC\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDailyConversationCounts(@org.jetbrains.annotations.NotNull()
    java.util.Date startDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.data.local.dao.DailyCount>> $completion);
    
    /**
     * 🎯 Get most common topics
     */
    @androidx.room.Query(value = "\n        SELECT user_input, COUNT(*) as frequency\n        FROM conversations\n        GROUP BY LOWER(SUBSTR(user_input, 1, 20))\n        ORDER BY frequency DESC\n        LIMIT :limit\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMostCommonTopics(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.data.local.dao.TopicFrequency>> $completion);
    
    /**
     * 💾 Insert conversation
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertConversation(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.entity.ConversationEntity conversation, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * 🔄 Update conversation
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateConversation(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.entity.ConversationEntity conversation, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * ⭐ Toggle favorite status
     */
    @androidx.room.Query(value = "UPDATE conversations SET is_favorite = NOT is_favorite WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object toggleFavorite(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 🌟 Rate conversation
     */
    @androidx.room.Query(value = "UPDATE conversations SET user_rating = :rating WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object rateConversation(long id, int rating, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 🗑️ Delete conversation
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteConversation(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.entity.ConversationEntity conversation, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 🗑️ Delete old conversations (cleanup)
     */
    @androidx.room.Query(value = "DELETE FROM conversations WHERE created_at < :cutoffDate")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteOldConversations(@org.jetbrains.annotations.NotNull()
    java.util.Date cutoffDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * 🧹 Clear all conversations
     */
    @androidx.room.Query(value = "DELETE FROM conversations")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearAllConversations(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 💬 Conversation DAO - God Mode Database Access
     *
     * Features:
     * - Reactive Flow queries
     * - Complex search capabilities
     * - Performance optimized
     * - Analytics support
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}