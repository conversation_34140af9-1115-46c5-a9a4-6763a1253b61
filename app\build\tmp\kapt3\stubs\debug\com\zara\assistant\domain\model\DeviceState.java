package com.zara.assistant.domain.model;

import java.util.*;

/**
 * 📱 Device State
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\u0006\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\nH\u00c6\u0003J;\u0010\u0018\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u00052\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u000eR\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u000eR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006\u001e"}, d2 = {"Lcom/zara/assistant/domain/model/DeviceState;", "", "batteryLevel", "", "isCharging", "", "networkType", "Lcom/zara/assistant/domain/model/NetworkType;", "isScreenOn", "orientation", "Lcom/zara/assistant/domain/model/DeviceOrientation;", "(IZLcom/zara/assistant/domain/model/NetworkType;ZLcom/zara/assistant/domain/model/DeviceOrientation;)V", "getBatteryLevel", "()I", "()Z", "getNetworkType", "()Lcom/zara/assistant/domain/model/NetworkType;", "getOrientation", "()Lcom/zara/assistant/domain/model/DeviceOrientation;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "toString", "", "app_debug"})
public final class DeviceState {
    private final int batteryLevel = 0;
    private final boolean isCharging = false;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.NetworkType networkType = null;
    private final boolean isScreenOn = false;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.DeviceOrientation orientation = null;
    
    public DeviceState(int batteryLevel, boolean isCharging, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.NetworkType networkType, boolean isScreenOn, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.DeviceOrientation orientation) {
        super();
    }
    
    public final int getBatteryLevel() {
        return 0;
    }
    
    public final boolean isCharging() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.NetworkType getNetworkType() {
        return null;
    }
    
    public final boolean isScreenOn() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.DeviceOrientation getOrientation() {
        return null;
    }
    
    public final int component1() {
        return 0;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.NetworkType component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.DeviceOrientation component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.DeviceState copy(int batteryLevel, boolean isCharging, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.NetworkType networkType, boolean isScreenOn, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.DeviceOrientation orientation) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}