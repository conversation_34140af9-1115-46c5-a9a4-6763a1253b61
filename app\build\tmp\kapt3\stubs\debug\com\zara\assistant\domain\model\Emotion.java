package com.zara.assistant.domain.model;

import java.util.*;

/**
 * 🎭 Emotion Types
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/zara/assistant/domain/model/Emotion;", "", "(Ljava/lang/String;I)V", "HAPPY", "SAD", "ANGRY", "EXCITED", "CALM", "FRUSTRATED", "SURPRISED", "NEUTRAL", "app_debug"})
public enum Emotion {
    /*public static final*/ HAPPY /* = new HAPPY() */,
    /*public static final*/ SAD /* = new SAD() */,
    /*public static final*/ ANGRY /* = new ANGRY() */,
    /*public static final*/ EXCITED /* = new EXCITED() */,
    /*public static final*/ CALM /* = new CALM() */,
    /*public static final*/ FRUSTRATED /* = new FRUSTRATED() */,
    /*public static final*/ SURPRISED /* = new SURPRISED() */,
    /*public static final*/ NEUTRAL /* = new NEUTRAL() */;
    
    Emotion() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.zara.assistant.domain.model.Emotion> getEntries() {
        return null;
    }
}