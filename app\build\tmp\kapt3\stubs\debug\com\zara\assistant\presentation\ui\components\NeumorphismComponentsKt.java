package com.zara.assistant.presentation.ui.components;

import androidx.compose.animation.*;
import androidx.compose.animation.core.*;
import androidx.compose.foundation.*;
import androidx.compose.foundation.layout.*;
import androidx.compose.foundation.lazy.LazyListScope;
import androidx.compose.material.icons.Icons;
import androidx.compose.material.icons.filled.*;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.graphics.Brush;
import androidx.compose.ui.graphics.vector.ImageVector;
import androidx.compose.ui.text.font.FontWeight;
import androidx.compose.ui.text.style.TextAlign;
import androidx.compose.ui.unit.Dp;
import com.zara.assistant.domain.model.Emotion;
import com.zara.assistant.presentation.theme.*;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000r\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0007\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a4\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u0003\u001a?\u0010\u000b\u001a\u00020\u00012\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\u0017\u0010\u0010\u001a\u0013\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u0011\u00a2\u0006\u0002\b\u0013H\u0003\u001a\\\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u00162\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0017\u001a\u00020\u00052\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00192\b\b\u0002\u0010\u001a\u001a\u00020\u001bH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u001c\u0010\u001d\u001aj\u0010\u001e\u001a\u00020\u00012\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u001a\u001a\u00020\u001b2\b\b\u0002\u0010\u001f\u001a\u00020\u001b2\u0010\b\u0002\u0010\u0015\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00162\u001c\u0010\u0010\u001a\u0018\u0012\u0004\u0012\u00020 \u0012\u0004\u0012\u00020\u00010\u0011\u00a2\u0006\u0002\b!\u00a2\u0006\u0002\b\u0013H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\"\u0010#\u001aV\u0010$\u001a\u00020\u00012\u0006\u0010%\u001a\u00020\u00052\u0006\u0010&\u001a\u00020\u00052\u0006\u0010\'\u001a\u00020\b2\u0006\u0010(\u001a\u00020)2\f\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00010\u00162\f\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00010\u00162\u0006\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\nH\u0007\u001a2\u0010,\u001a\u00020\u00012\u0006\u0010-\u001a\u00020\u00032\u0006\u0010.\u001a\u00020\u00032\u0006\u0010\'\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\nH\u0007\u001aJ\u0010/\u001a\u00020\u00012\u0012\u00100\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00112\f\u00101\u001a\b\u0012\u0004\u0012\u00020\u00010\u00162\f\u00102\u001a\b\u0012\u0004\u0012\u00020\u00010\u00162\u0006\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\nH\u0007\u001a\u0015\u00103\u001a\u0002042\u0006\u0010\u0007\u001a\u00020\bH\u0007\u00a2\u0006\u0002\u00105\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00066"}, d2 = {"ConversationBubble", "", "text", "", "isUser", "", "isDarkMode", "emotion", "Lcom/zara/assistant/domain/model/Emotion;", "modifier", "Landroidx/compose/ui/Modifier;", "LazyRow", "horizontalArrangement", "Landroidx/compose/foundation/layout/Arrangement$Horizontal;", "contentPadding", "Landroidx/compose/foundation/layout/PaddingValues;", "content", "Lkotlin/Function1;", "Landroidx/compose/foundation/lazy/LazyListScope;", "Lkotlin/ExtensionFunctionType;", "NeumorphismButton", "onClick", "Lkotlin/Function0;", "enabled", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "elevation", "Landroidx/compose/ui/unit/Dp;", "NeumorphismButton-6ZxE2Lo", "(Ljava/lang/String;Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/Modifier;ZZLandroidx/compose/ui/graphics/vector/ImageVector;F)V", "NeumorphismCard", "cornerRadius", "Landroidx/compose/foundation/layout/BoxScope;", "Landroidx/compose/runtime/Composable;", "NeumorphismCard-T43hY1o", "(Landroidx/compose/ui/Modifier;ZFFLkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;)V", "VoiceVisualizationButton", "isListening", "isSpeaking", "currentEmotion", "voiceWaveScale", "", "onStartVoice", "onStopVoice", "ZaraConversationDisplay", "lastUserInput", "lastResponse", "ZaraQuickActions", "onSendMessage", "onClearConversation", "onToggleHistory", "getEmotionColor", "Landroidx/compose/ui/graphics/Color;", "(Lcom/zara/assistant/domain/model/Emotion;)J", "app_debug"})
public final class NeumorphismComponentsKt {
    
    /**
     * 🎙️ Voice Visualization Button
     */
    @androidx.compose.runtime.Composable()
    public static final void VoiceVisualizationButton(boolean isListening, boolean isSpeaking, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Emotion currentEmotion, float voiceWaveScale, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onStartVoice, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onStopVoice, boolean isDarkMode, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 💬 Conversation Display
     */
    @androidx.compose.runtime.Composable()
    public static final void ZaraConversationDisplay(@org.jetbrains.annotations.NotNull()
    java.lang.String lastUserInput, @org.jetbrains.annotations.NotNull()
    java.lang.String lastResponse, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Emotion currentEmotion, boolean isDarkMode, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 💭 Conversation Bubble
     */
    @androidx.compose.runtime.Composable()
    private static final void ConversationBubble(java.lang.String text, boolean isUser, boolean isDarkMode, com.zara.assistant.domain.model.Emotion emotion, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * ⚡ Quick Actions
     */
    @androidx.compose.runtime.Composable()
    public static final void ZaraQuickActions(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSendMessage, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClearConversation, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onToggleHistory, boolean isDarkMode, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 🎨 Get emotion color
     */
    @androidx.compose.runtime.Composable()
    public static final long getEmotionColor(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Emotion emotion) {
        return 0L;
    }
    
    /**
     * 📱 Lazy Row for horizontal scrolling
     */
    @androidx.compose.runtime.Composable()
    private static final void LazyRow(androidx.compose.ui.Modifier modifier, androidx.compose.foundation.layout.Arrangement.Horizontal horizontalArrangement, androidx.compose.foundation.layout.PaddingValues contentPadding, kotlin.jvm.functions.Function1<? super androidx.compose.foundation.lazy.LazyListScope, kotlin.Unit> content) {
    }
}