package com.zara.assistant.domain.model;

import java.util.*;

/**
 * 😊 Emotion Detection Data
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0005H\u00c6\u0003J=\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000b\u00a8\u0006\u001e"}, d2 = {"Lcom/zara/assistant/domain/model/EmotionData;", "", "primary", "Lcom/zara/assistant/domain/model/Emotion;", "confidence", "", "secondary", "arousal", "valence", "(Lcom/zara/assistant/domain/model/Emotion;FLcom/zara/assistant/domain/model/Emotion;FF)V", "getArousal", "()F", "getConfidence", "getPrimary", "()Lcom/zara/assistant/domain/model/Emotion;", "getSecondary", "getValence", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class EmotionData {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.Emotion primary = null;
    private final float confidence = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final com.zara.assistant.domain.model.Emotion secondary = null;
    private final float arousal = 0.0F;
    private final float valence = 0.0F;
    
    public EmotionData(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Emotion primary, float confidence, @org.jetbrains.annotations.Nullable()
    com.zara.assistant.domain.model.Emotion secondary, float arousal, float valence) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.Emotion getPrimary() {
        return null;
    }
    
    public final float getConfidence() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.zara.assistant.domain.model.Emotion getSecondary() {
        return null;
    }
    
    public final float getArousal() {
        return 0.0F;
    }
    
    public final float getValence() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.Emotion component1() {
        return null;
    }
    
    public final float component2() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.zara.assistant.domain.model.Emotion component3() {
        return null;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.EmotionData copy(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Emotion primary, float confidence, @org.jetbrains.annotations.Nullable()
    com.zara.assistant.domain.model.Emotion secondary, float arousal, float valence) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}