package com.zara.assistant.presentation.navigation;

import androidx.compose.animation.*;
import androidx.compose.runtime.Composable;
import androidx.compose.ui.Modifier;
import androidx.navigation.NavHostController;
import com.zara.assistant.presentation.ui.*;

/**
 * 🎨 Custom Navigation Animations
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u000b\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u0006R\u0011\u0010\r\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u0006R\u0011\u0010\u000f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\nR\u0011\u0010\u0011\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\n\u00a8\u0006\u0013"}, d2 = {"Lcom/zara/assistant/presentation/navigation/ZaraAnimations;", "", "()V", "scaleInFade", "Landroidx/compose/animation/EnterTransition;", "getScaleInFade", "()Landroidx/compose/animation/EnterTransition;", "scaleOutFade", "Landroidx/compose/animation/ExitTransition;", "getScaleOutFade", "()Landroidx/compose/animation/ExitTransition;", "slideInFromBottom", "getSlideInFromBottom", "slideInFromRight", "getSlideInFromRight", "slideOutToBottom", "getSlideOutToBottom", "slideOutToLeft", "getSlideOutToLeft", "app_debug"})
public final class ZaraAnimations {
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.EnterTransition slideInFromRight = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.ExitTransition slideOutToLeft = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.EnterTransition slideInFromBottom = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.ExitTransition slideOutToBottom = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.EnterTransition scaleInFade = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.ExitTransition scaleOutFade = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.presentation.navigation.ZaraAnimations INSTANCE = null;
    
    private ZaraAnimations() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.EnterTransition getSlideInFromRight() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.ExitTransition getSlideOutToLeft() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.EnterTransition getSlideInFromBottom() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.ExitTransition getSlideOutToBottom() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.EnterTransition getScaleInFade() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.ExitTransition getScaleOutFade() {
        return null;
    }
}