package com.zara.assistant.presentation.theme;

import androidx.compose.material3.*;
import androidx.compose.runtime.Composable;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\f\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0019\u0010\u0003\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006R\u0019\u0010\b\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\t\u0010\u0006R\u0019\u0010\n\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000b\u0010\u0006R\u0019\u0010\f\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\r\u0010\u0006R\u0019\u0010\u000e\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000f\u0010\u0006\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006\u0010"}, d2 = {"Lcom/zara/assistant/presentation/theme/VoiceStateColors;", "", "()V", "Error", "Landroidx/compose/ui/graphics/Color;", "getError-0d7_KjU", "()J", "J", "Idle", "getIdle-0d7_KjU", "Listening", "getListening-0d7_KjU", "Processing", "getProcessing-0d7_KjU", "Speaking", "getSpeaking-0d7_KjU", "app_debug"})
public final class VoiceStateColors {
    private static final long Idle = 0L;
    private static final long Listening = 0L;
    private static final long Processing = 0L;
    private static final long Speaking = 0L;
    private static final long Error = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.presentation.theme.VoiceStateColors INSTANCE = null;
    
    private VoiceStateColors() {
        super();
    }
}