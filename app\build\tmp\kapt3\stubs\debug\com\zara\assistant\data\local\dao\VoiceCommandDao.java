package com.zara.assistant.data.local.dao;

import androidx.room.*;
import com.zara.assistant.data.local.entity.VoiceCommandEntity;
import com.zara.assistant.domain.model.Emotion;
import kotlinx.coroutines.flow.Flow;
import java.util.Date;

/**
 * 🎙️ Voice Command DAO - God Mode Database Access
 *
 * Features:
 * - Voice analytics tracking
 * - Performance monitoring
 * - Success rate analysis
 * - Quality metrics
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000t\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bg\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\u00072\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012J\u001c\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00150\u00142\u0006\u0010\u0016\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u001c\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00140\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\'J\u001c\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00140\u00182\u0006\u0010\u001c\u001a\u00020\u001dH\'J\u001c\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001f0\u00142\u0006\u0010\u0016\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0014\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00140\u0018H\'J\u001e\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00140\u00182\b\b\u0002\u0010\"\u001a\u00020#H\'J\u001e\u0010$\u001a\b\u0012\u0004\u0012\u00020%0\u00142\b\b\u0002\u0010&\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\'J\u0014\u0010(\u001a\b\u0012\u0004\u0012\u00020)0\u0014H\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0014\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00140\u0018H\'J\u0016\u0010+\u001a\u00020,2\u0006\u0010\u0016\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ(\u0010-\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00140\u00182\b\b\u0002\u0010&\u001a\u00020\u00072\b\b\u0002\u0010.\u001a\u00020\u0007H\'J\u0016\u0010/\u001a\u00020\u00052\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012J\u0016\u00100\u001a\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012\u00a8\u00061"}, d2 = {"Lcom/zara/assistant/data/local/dao/VoiceCommandDao;", "", "addUserFeedback", "", "id", "", "feedback", "", "(JILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearAllCommands", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldCommands", "cutoffDate", "Ljava/util/Date;", "(Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteVoiceCommand", "command", "Lcom/zara/assistant/data/local/entity/VoiceCommandEntity;", "(Lcom/zara/assistant/data/local/entity/VoiceCommandEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAudioQualityTrends", "", "Lcom/zara/assistant/data/local/dao/AudioQualityTrend;", "startDate", "getCommandsByEmotion", "Lkotlinx/coroutines/flow/Flow;", "emotion", "Lcom/zara/assistant/domain/model/Emotion;", "getCommandsByType", "type", "", "getDailyCommandStats", "Lcom/zara/assistant/data/local/dao/DailyCommandStats;", "getFailedCommands", "getHighConfidenceCommands", "threshold", "", "getMostUsedCommands", "Lcom/zara/assistant/data/local/dao/CommandUsage;", "limit", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSuccessRateByType", "Lcom/zara/assistant/data/local/dao/CommandTypeStats;", "getSuccessfulCommands", "getVoiceAnalytics", "Lcom/zara/assistant/data/local/dao/VoiceAnalytics;", "getVoiceCommands", "offset", "insertVoiceCommand", "updateVoiceCommand", "app_debug"})
@androidx.room.Dao()
public abstract interface VoiceCommandDao {
    
    /**
     * 📊 Get all voice commands with pagination
     */
    @androidx.room.Query(value = "SELECT * FROM voice_commands ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.VoiceCommandEntity>> getVoiceCommands(int limit, int offset);
    
    /**
     * 🎯 Get commands by type
     */
    @androidx.room.Query(value = "SELECT * FROM voice_commands WHERE command_type = :type ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.VoiceCommandEntity>> getCommandsByType(@org.jetbrains.annotations.NotNull()
    java.lang.String type);
    
    /**
     * ✅ Get successful commands only
     */
    @androidx.room.Query(value = "SELECT * FROM voice_commands WHERE success = 1 ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.VoiceCommandEntity>> getSuccessfulCommands();
    
    /**
     * ❌ Get failed commands for analysis
     */
    @androidx.room.Query(value = "SELECT * FROM voice_commands WHERE success = 0 ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.VoiceCommandEntity>> getFailedCommands();
    
    /**
     * 📈 Get commands by confidence threshold
     */
    @androidx.room.Query(value = "SELECT * FROM voice_commands WHERE confidence_score >= :threshold ORDER BY confidence_score DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.VoiceCommandEntity>> getHighConfidenceCommands(float threshold);
    
    /**
     * 😊 Get commands by emotion
     */
    @androidx.room.Query(value = "SELECT * FROM voice_commands WHERE emotion = :emotion ORDER BY created_at DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.entity.VoiceCommandEntity>> getCommandsByEmotion(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Emotion emotion);
    
    /**
     * 📊 Get voice command analytics
     */
    @androidx.room.Query(value = "\n        SELECT \n            COUNT(*) as total_commands,\n            AVG(confidence_score) as avg_confidence,\n            AVG(processing_time_ms) as avg_processing_time,\n            AVG(audio_quality_score) as avg_audio_quality,\n            COUNT(CASE WHEN success = 1 THEN 1 END) * 100.0 / COUNT(*) as success_rate,\n            COUNT(CASE WHEN wake_word_detected = 1 THEN 1 END) * 100.0 / COUNT(*) as wake_word_rate\n        FROM voice_commands\n        WHERE created_at >= :startDate\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getVoiceAnalytics(@org.jetbrains.annotations.NotNull()
    java.util.Date startDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.data.local.dao.VoiceAnalytics> $completion);
    
    /**
     * 📈 Get success rate by command type
     */
    @androidx.room.Query(value = "\n        SELECT \n            command_type,\n            COUNT(*) as total_count,\n            COUNT(CASE WHEN success = 1 THEN 1 END) * 100.0 / COUNT(*) as success_rate,\n            AVG(confidence_score) as avg_confidence\n        FROM voice_commands\n        GROUP BY command_type\n        ORDER BY success_rate DESC\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSuccessRateByType(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.data.local.dao.CommandTypeStats>> $completion);
    
    /**
     * 🎯 Get most used commands
     */
    @androidx.room.Query(value = "\n        SELECT command_text, COUNT(*) as usage_count\n        FROM voice_commands\n        WHERE success = 1\n        GROUP BY LOWER(command_text)\n        ORDER BY usage_count DESC\n        LIMIT :limit\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMostUsedCommands(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.data.local.dao.CommandUsage>> $completion);
    
    /**
     * 📅 Get daily command counts
     */
    @androidx.room.Query(value = "\n        SELECT \n            DATE(created_at/1000, \'unixepoch\') as date, \n            COUNT(*) as total_count,\n            COUNT(CASE WHEN success = 1 THEN 1 END) as success_count\n        FROM voice_commands \n        WHERE created_at >= :startDate\n        GROUP BY DATE(created_at/1000, \'unixepoch\')\n        ORDER BY date DESC\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDailyCommandStats(@org.jetbrains.annotations.NotNull()
    java.util.Date startDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.data.local.dao.DailyCommandStats>> $completion);
    
    /**
     * 🔊 Get audio quality trends
     */
    @androidx.room.Query(value = "\n        SELECT \n            DATE(created_at/1000, \'unixepoch\') as date,\n            AVG(audio_quality_score) as avg_quality,\n            AVG(background_noise_level) as avg_noise,\n            AVG(confidence_score) as avg_confidence\n        FROM voice_commands\n        WHERE created_at >= :startDate\n        GROUP BY DATE(created_at/1000, \'unixepoch\')\n        ORDER BY date DESC\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAudioQualityTrends(@org.jetbrains.annotations.NotNull()
    java.util.Date startDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.data.local.dao.AudioQualityTrend>> $completion);
    
    /**
     * 💾 Insert voice command
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertVoiceCommand(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.entity.VoiceCommandEntity command, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * 🔄 Update voice command
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateVoiceCommand(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.entity.VoiceCommandEntity command, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 🌟 Add user feedback
     */
    @androidx.room.Query(value = "UPDATE voice_commands SET user_feedback = :feedback WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addUserFeedback(long id, int feedback, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 🗑️ Delete voice command
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteVoiceCommand(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.entity.VoiceCommandEntity command, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 🗑️ Delete old commands (cleanup)
     */
    @androidx.room.Query(value = "DELETE FROM voice_commands WHERE created_at < :cutoffDate")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteOldCommands(@org.jetbrains.annotations.NotNull()
    java.util.Date cutoffDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * 🧹 Clear all voice commands
     */
    @androidx.room.Query(value = "DELETE FROM voice_commands")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearAllCommands(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 🎙️ Voice Command DAO - God Mode Database Access
     *
     * Features:
     * - Voice analytics tracking
     * - Performance monitoring
     * - Success rate analysis
     * - Quality metrics
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}