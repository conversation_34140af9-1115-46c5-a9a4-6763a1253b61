package com.zara.assistant.presentation.navigation;

import androidx.compose.animation.*;
import androidx.compose.runtime.Composable;
import androidx.compose.ui.Modifier;
import androidx.navigation.NavHostController;
import com.zara.assistant.presentation.ui.*;

/**
 * 📍 Navigation Routes
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/zara/assistant/presentation/navigation/ZaraRoutes;", "", "()V", "ABOUT", "", "AI_SETTINGS", "CONVERSATION", "MAIN", "SETTINGS", "SYSTEM_CONTROLS", "VOICE_TRAINING", "app_debug"})
public final class ZaraRoutes {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MAIN = "main";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SETTINGS = "settings";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CONVERSATION = "conversation";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String VOICE_TRAINING = "voice_training";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SYSTEM_CONTROLS = "system_controls";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String AI_SETTINGS = "ai_settings";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ABOUT = "about";
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.presentation.navigation.ZaraRoutes INSTANCE = null;
    
    private ZaraRoutes() {
        super();
    }
}