package com.zara.assistant.domain.model;

import java.util.*;

/**
 * 📝 Response Types
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/zara/assistant/domain/model/ResponseType;", "", "(Ljava/lang/String;I)V", "INFORMATION", "SYSTEM_CONTROL", "CONVERSATION", "ERROR", "CONFIRMATION", "app_debug"})
public enum ResponseType {
    /*public static final*/ INFORMATION /* = new INFORMATION() */,
    /*public static final*/ SYSTEM_CONTROL /* = new SYSTEM_CONTROL() */,
    /*public static final*/ CONVERSATION /* = new CONVERSATION() */,
    /*public static final*/ ERROR /* = new ERROR() */,
    /*public static final*/ CONFIRMATION /* = new CONFIRMATION() */;
    
    ResponseType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.zara.assistant.domain.model.ResponseType> getEntries() {
        return null;
    }
}