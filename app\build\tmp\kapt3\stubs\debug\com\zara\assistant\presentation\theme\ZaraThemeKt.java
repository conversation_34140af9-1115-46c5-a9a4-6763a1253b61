package com.zara.assistant.presentation.theme;

import androidx.compose.material3.*;
import androidx.compose.runtime.Composable;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u001a%\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000e2\u0011\u0010\u000f\u001a\r\u0012\u0004\u0012\u00020\f0\u0010\u00a2\u0006\u0002\b\u0011H\u0007\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u000e\u0010\u0002\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0012"}, d2 = {"DarkColorScheme", "Landroidx/compose/material3/ColorScheme;", "LightColorScheme", "ZaraShapes", "Landroidx/compose/material3/Shapes;", "getZaraShapes", "()Landroidx/compose/material3/Shapes;", "ZaraTypography", "Landroidx/compose/material3/Typography;", "getZaraTypography", "()Landroidx/compose/material3/Typography;", "ZaraTheme", "", "darkTheme", "", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "app_debug"})
public final class ZaraThemeKt {
    
    /**
     * 🎨 Zara Theme - God Mode Neumorphism Design
     *
     * Features:
     * - Beautiful neumorphism colors
     * - Dark/Light mode support
     * - 2024-2025 design trends
     * - Accessibility optimized
     * - Performance focused
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.ColorScheme DarkColorScheme = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.ColorScheme LightColorScheme = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.Typography ZaraTypography = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.Shapes ZaraShapes = null;
    
    @androidx.compose.runtime.Composable()
    public static final void ZaraTheme(boolean darkTheme, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.material3.Typography getZaraTypography() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.material3.Shapes getZaraShapes() {
        return null;
    }
}