package com.zara.assistant.domain.model;

import java.util.*;

/**
 * ⚡ Action Types
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0010\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010\u00a8\u0006\u0011"}, d2 = {"Lcom/zara/assistant/domain/model/ActionType;", "", "(Ljava/lang/String;I)V", "OPEN_APP", "CLOSE_APP", "ADJUST_VOLUME", "TOGGLE_WIFI", "TOGGLE_BLUETOOTH", "MAKE_CALL", "SEND_MESSAGE", "SET_ALARM", "CREATE_REMINDER", "PLAY_MUSIC", "TAKE_PHOTO", "TOGGLE_FLASHLIGHT", "ADJUST_BRIGHTNESS", "ENABLE_DND", "app_debug"})
public enum ActionType {
    /*public static final*/ OPEN_APP /* = new OPEN_APP() */,
    /*public static final*/ CLOSE_APP /* = new CLOSE_APP() */,
    /*public static final*/ ADJUST_VOLUME /* = new ADJUST_VOLUME() */,
    /*public static final*/ TOGGLE_WIFI /* = new TOGGLE_WIFI() */,
    /*public static final*/ TOGGLE_BLUETOOTH /* = new TOGGLE_BLUETOOTH() */,
    /*public static final*/ MAKE_CALL /* = new MAKE_CALL() */,
    /*public static final*/ SEND_MESSAGE /* = new SEND_MESSAGE() */,
    /*public static final*/ SET_ALARM /* = new SET_ALARM() */,
    /*public static final*/ CREATE_REMINDER /* = new CREATE_REMINDER() */,
    /*public static final*/ PLAY_MUSIC /* = new PLAY_MUSIC() */,
    /*public static final*/ TAKE_PHOTO /* = new TAKE_PHOTO() */,
    /*public static final*/ TOGGLE_FLASHLIGHT /* = new TOGGLE_FLASHLIGHT() */,
    /*public static final*/ ADJUST_BRIGHTNESS /* = new ADJUST_BRIGHTNESS() */,
    /*public static final*/ ENABLE_DND /* = new ENABLE_DND() */;
    
    ActionType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.zara.assistant.domain.model.ActionType> getEntries() {
        return null;
    }
}