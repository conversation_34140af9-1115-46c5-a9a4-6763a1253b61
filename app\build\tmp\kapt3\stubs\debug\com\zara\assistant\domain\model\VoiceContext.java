package com.zara.assistant.domain.model;

import java.util.*;

/**
 * 📍 Voice Context - Situational awareness
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0007H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u000bH\u00c6\u0003J?\u0010\u001c\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020!H\u00d6\u0001J\t\u0010\"\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0013\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006#"}, d2 = {"Lcom/zara/assistant/domain/model/VoiceContext;", "", "location", "", "timeOfDay", "Lcom/zara/assistant/domain/model/TimeOfDay;", "deviceState", "Lcom/zara/assistant/domain/model/DeviceState;", "userActivity", "Lcom/zara/assistant/domain/model/UserActivity;", "ambientNoise", "", "(Ljava/lang/String;Lcom/zara/assistant/domain/model/TimeOfDay;Lcom/zara/assistant/domain/model/DeviceState;Lcom/zara/assistant/domain/model/UserActivity;F)V", "getAmbientNoise", "()F", "getDeviceState", "()Lcom/zara/assistant/domain/model/DeviceState;", "getLocation", "()Ljava/lang/String;", "getTimeOfDay", "()Lcom/zara/assistant/domain/model/TimeOfDay;", "getUserActivity", "()Lcom/zara/assistant/domain/model/UserActivity;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class VoiceContext {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String location = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.TimeOfDay timeOfDay = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.DeviceState deviceState = null;
    @org.jetbrains.annotations.Nullable()
    private final com.zara.assistant.domain.model.UserActivity userActivity = null;
    private final float ambientNoise = 0.0F;
    
    public VoiceContext(@org.jetbrains.annotations.Nullable()
    java.lang.String location, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.TimeOfDay timeOfDay, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.DeviceState deviceState, @org.jetbrains.annotations.Nullable()
    com.zara.assistant.domain.model.UserActivity userActivity, float ambientNoise) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getLocation() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.TimeOfDay getTimeOfDay() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.DeviceState getDeviceState() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.zara.assistant.domain.model.UserActivity getUserActivity() {
        return null;
    }
    
    public final float getAmbientNoise() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.TimeOfDay component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.DeviceState component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.zara.assistant.domain.model.UserActivity component4() {
        return null;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.VoiceContext copy(@org.jetbrains.annotations.Nullable()
    java.lang.String location, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.TimeOfDay timeOfDay, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.DeviceState deviceState, @org.jetbrains.annotations.Nullable()
    com.zara.assistant.domain.model.UserActivity userActivity, float ambientNoise) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}