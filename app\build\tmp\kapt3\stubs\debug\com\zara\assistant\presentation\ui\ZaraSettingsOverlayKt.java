package com.zara.assistant.presentation.ui;

import androidx.compose.animation.*;
import androidx.compose.foundation.*;
import androidx.compose.foundation.layout.*;
import androidx.compose.material.icons.Icons;
import androidx.compose.material.icons.filled.*;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.text.font.FontWeight;
import androidx.compose.ui.window.DialogProperties;
import com.zara.assistant.domain.model.AIProvider;
import com.zara.assistant.presentation.theme.*;
import com.zara.assistant.presentation.ui.components.*;
import com.zara.assistant.presentation.viewmodel.ZaraViewModel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000D\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a,\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0003\u001a.\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u00072\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\f2\u0006\u0010\u0006\u001a\u00020\u0007H\u0003\u001a,\u0010\r\u001a\u00020\u00012\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\f2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\f2\u0006\u0010\u0006\u001a\u00020\u0007H\u0003\u001a\u0016\u0010\u0010\u001a\u00020\u00012\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\fH\u0003\u001a \u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0006\u001a\u00020\u0007H\u0003\u001a\u0018\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0006\u001a\u00020\u0007H\u0003\u001aB\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u00072\u0012\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\u001b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\f2\u0006\u0010\u0006\u001a\u00020\u0007H\u0003\u001a\u001e\u0010\u001f\u001a\u00020\u00012\u0006\u0010 \u001a\u00020!2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\fH\u0007\u001a\u0010\u0010\"\u001a\u00020\u00142\u0006\u0010\t\u001a\u00020\u0003H\u0002\u00a8\u0006#"}, d2 = {"AISettingsSection", "", "currentProvider", "Lcom/zara/assistant/domain/model/AIProvider;", "onProviderChange", "Lkotlin/Function1;", "isDarkMode", "", "ProviderOption", "provider", "isSelected", "onSelect", "Lkotlin/Function0;", "SettingsActions", "onClearConversation", "onResetStats", "SettingsHeader", "onDismiss", "StatCard", "title", "", "value", "StatisticsSection", "sessionStats", "Lcom/zara/assistant/presentation/viewmodel/SessionStats;", "VoiceSettingsSection", "wakeWordSensitivity", "", "wakeWordActive", "onSensitivityChange", "onToggleWakeWord", "ZaraSettingsOverlay", "viewModel", "Lcom/zara/assistant/presentation/viewmodel/ZaraViewModel;", "getProviderDescription", "app_debug"})
public final class ZaraSettingsOverlayKt {
    
    /**
     * ⚙️ Zara Settings Overlay - God Mode Configuration
     *
     * Features:
     * - Beautiful neumorphism design
     * - Real-time settings updates
     * - Smooth animations
     * - Comprehensive controls
     */
    @androidx.compose.runtime.Composable()
    public static final void ZaraSettingsOverlay(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.presentation.viewmodel.ZaraViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    /**
     * 📱 Settings Header
     */
    @androidx.compose.runtime.Composable()
    private static final void SettingsHeader(kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    /**
     * 🎙️ Voice Settings Section
     */
    @androidx.compose.runtime.Composable()
    private static final void VoiceSettingsSection(float wakeWordSensitivity, boolean wakeWordActive, kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onSensitivityChange, kotlin.jvm.functions.Function0<kotlin.Unit> onToggleWakeWord, boolean isDarkMode) {
    }
    
    /**
     * 🤖 AI Settings Section
     */
    @androidx.compose.runtime.Composable()
    private static final void AISettingsSection(com.zara.assistant.domain.model.AIProvider currentProvider, kotlin.jvm.functions.Function1<? super com.zara.assistant.domain.model.AIProvider, kotlin.Unit> onProviderChange, boolean isDarkMode) {
    }
    
    /**
     * 🎯 Provider Option
     */
    @androidx.compose.runtime.Composable()
    private static final void ProviderOption(com.zara.assistant.domain.model.AIProvider provider, boolean isSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onSelect, boolean isDarkMode) {
    }
    
    /**
     * 📊 Statistics Section
     */
    @androidx.compose.runtime.Composable()
    private static final void StatisticsSection(com.zara.assistant.presentation.viewmodel.SessionStats sessionStats, boolean isDarkMode) {
    }
    
    /**
     * 📈 Stat Card
     */
    @androidx.compose.runtime.Composable()
    private static final void StatCard(java.lang.String title, java.lang.String value, boolean isDarkMode) {
    }
    
    /**
     * ⚡ Settings Actions
     */
    @androidx.compose.runtime.Composable()
    private static final void SettingsActions(kotlin.jvm.functions.Function0<kotlin.Unit> onClearConversation, kotlin.jvm.functions.Function0<kotlin.Unit> onResetStats, boolean isDarkMode) {
    }
    
    /**
     * 📝 Get provider description
     */
    private static final java.lang.String getProviderDescription(com.zara.assistant.domain.model.AIProvider provider) {
        return null;
    }
}