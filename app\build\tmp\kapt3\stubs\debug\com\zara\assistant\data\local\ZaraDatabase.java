package com.zara.assistant.data.local;

import androidx.room.*;
import androidx.sqlite.db.SupportSQLiteDatabase;
import com.zara.assistant.data.local.dao.ConversationDao;
import com.zara.assistant.data.local.dao.VoiceCommandDao;
import com.zara.assistant.data.local.entity.ConversationEntity;
import com.zara.assistant.data.local.entity.VoiceCommandEntity;
import com.zara.assistant.data.local.converter.DateConverter;
import com.zara.assistant.data.local.converter.EmotionConverter;

/**
 * 🗄️ Zara Database - God Mode Local Storage
 *
 * Features:
 * - Room database with migrations
 * - Conversation history storage
 * - Voice command analytics
 * - Performance optimized
 * - Type converters for complex data
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \u00072\u00020\u0001:\u0001\u0007B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&\u00a8\u0006\b"}, d2 = {"Lcom/zara/assistant/data/local/ZaraDatabase;", "Landroidx/room/RoomDatabase;", "()V", "conversationDao", "Lcom/zara/assistant/data/local/dao/ConversationDao;", "voiceCommandDao", "Lcom/zara/assistant/data/local/dao/VoiceCommandDao;", "Companion", "app_debug"})
@androidx.room.Database(entities = {com.zara.assistant.data.local.entity.ConversationEntity.class, com.zara.assistant.data.local.entity.VoiceCommandEntity.class}, version = 1, exportSchema = true)
@androidx.room.TypeConverters(value = {com.zara.assistant.data.local.converter.DateConverter.class, com.zara.assistant.data.local.converter.EmotionConverter.class})
public abstract class ZaraDatabase extends androidx.room.RoomDatabase {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DATABASE_NAME = "zara_database";
    
    /**
     * 🔄 Database migrations for future versions
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.room.migration.Migration MIGRATION_1_2 = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.room.migration.Migration MIGRATION_2_3 = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.data.local.ZaraDatabase.Companion Companion = null;
    
    public ZaraDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.zara.assistant.data.local.dao.ConversationDao conversationDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.zara.assistant.data.local.dao.VoiceCommandDao voiceCommandDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\b\u00a8\u0006\u000b"}, d2 = {"Lcom/zara/assistant/data/local/ZaraDatabase$Companion;", "", "()V", "DATABASE_NAME", "", "MIGRATION_1_2", "Landroidx/room/migration/Migration;", "getMIGRATION_1_2", "()Landroidx/room/migration/Migration;", "MIGRATION_2_3", "getMIGRATION_2_3", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 🔄 Database migrations for future versions
         */
        @org.jetbrains.annotations.NotNull()
        public final androidx.room.migration.Migration getMIGRATION_1_2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.room.migration.Migration getMIGRATION_2_3() {
            return null;
        }
    }
}