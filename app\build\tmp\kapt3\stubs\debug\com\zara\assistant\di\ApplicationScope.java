package com.zara.assistant.di;

import android.content.Context;
import androidx.room.Room;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.zara.assistant.data.local.ZaraDatabase;
import com.zara.assistant.data.repository.ConversationRepository;
import com.zara.assistant.data.repository.UserPreferencesRepository;
import com.zara.assistant.services.*;
import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.android.qualifiers.ApplicationContext;
import dagger.hilt.components.SingletonComponent;
import kotlinx.coroutines.Dispatchers;
import javax.inject.Qualifier;
import javax.inject.Singleton;

/**
 * 🏗️ Zara Dependency Injection Module - God Mode Architecture
 *
 * Features:
 * - Clean dependency injection
 * - Singleton services
 * - Proper scoping
 * - Performance optimized
 */
@javax.inject.Qualifier()
@kotlin.annotation.Retention(value = kotlin.annotation.AnnotationRetention.BINARY)
@java.lang.annotation.Retention(value = java.lang.annotation.RetentionPolicy.CLASS)
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\n\n\u0002\u0018\u0002\n\u0002\u0010\u001b\n\u0000\b\u0087\u0002\u0018\u00002\u00020\u0001B\u0000\u00a8\u0006\u0002"}, d2 = {"Lcom/zara/assistant/di/ApplicationScope;", "", "app_debug"})
public abstract @interface ApplicationScope {
}