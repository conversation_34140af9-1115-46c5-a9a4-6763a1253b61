package com.zara.assistant.services

import android.util.Log
import com.zara.assistant.core.ApiKeyManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton
import com.zara.assistant.di.ApplicationScope
import kotlinx.coroutines.CoroutineScope

/**
 * 🟡 Perplexity AI Service - God Mode Implementation
 * 
 * Features:
 * - Real Perplexity API integration
 * - Real-time search and answers
 * - Source citations
 * - Related questions
 * - Performance optimization
 */
@Singleton
class PerplexityService @Inject constructor(
    @ApplicationScope private val applicationScope: CoroutineScope
) {
    
    companion object {
        private const val TAG = "PerplexityService"
        private const val PERPLEXITY_API_URL = "https://api.perplexity.ai/chat/completions"
        private const val MODEL = "llama-3.1-sonar-small-128k-online"
    }
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(15, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(45, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    
    /**
     * 🔍 Search and answer using Perplexity API
     */
    suspend fun searchAndAnswer(
        query: String,
        context: String = ""
    ): PerplexityResponse = withContext(Dispatchers.IO) {
        val startTime = System.currentTimeMillis()
        
        try {
            Log.d(TAG, "🟡 Searching with Perplexity: '$query'")

            // Get API key from ApiKeyManager
            val apiKey = ApiKeyManager.getPerplexityApiKey()
            if (apiKey == null) {
                throw Exception("Perplexity API key not found")
            }

            val requestBody = createSearchRequestBody(query, context)
            val request = Request.Builder()
                .url(PERPLEXITY_API_URL)
                .addHeader("Authorization", "Bearer $apiKey")
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build()
            
            val response = client.newCall(request).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                val parsedResponse = parseSearchResponse(responseBody)
                
                val processingTime = System.currentTimeMillis() - startTime
                Log.d(TAG, "✅ Perplexity response generated in ${processingTime}ms")
                
                parsedResponse.copy(processingTime = processingTime)
            } else {
                Log.e(TAG, "❌ Perplexity API error: ${response.code} - ${response.message}")
                
                PerplexityResponse(
                    text = "I couldn't search for that information right now. Please try again.",
                    confidence = 0.3f,
                    sources = emptyList(),
                    relatedQuestions = emptyList(),
                    processingTime = System.currentTimeMillis() - startTime
                )
            }
            
        } catch (e: IOException) {
            Log.e(TAG, "❌ Network error with Perplexity API", e)
            
            PerplexityResponse(
                text = "I'm having trouble accessing real-time information. Please check your connection.",
                confidence = 0.2f,
                sources = emptyList(),
                relatedQuestions = emptyList(),
                processingTime = System.currentTimeMillis() - startTime
            )
        } catch (e: Exception) {
            Log.e(TAG, "❌ Unexpected error with Perplexity API", e)
            
            PerplexityResponse(
                text = "I encountered an error while searching. Please try again.",
                confidence = 0.1f,
                sources = emptyList(),
                relatedQuestions = emptyList(),
                processingTime = System.currentTimeMillis() - startTime
            )
        }
    }
    
    /**
     * 📝 Create search request body
     */
    private fun createSearchRequestBody(query: String, context: String): RequestBody {
        val messages = JSONArray().apply {
            put(JSONObject().apply {
                put("role", "system")
                put("content", "You are Zara, a helpful voice assistant. Provide accurate, concise answers with real-time information. Include sources when possible.")
            })
            
            if (context.isNotEmpty()) {
                put(JSONObject().apply {
                    put("role", "user")
                    put("content", "Context: $context")
                })
            }
            
            put(JSONObject().apply {
                put("role", "user")
                put("content", query)
            })
        }
        
        val json = JSONObject().apply {
            put("model", MODEL)
            put("messages", messages)
            put("max_tokens", 500)
            put("temperature", 0.3) // Lower temperature for factual accuracy
            put("top_p", 0.9)
            put("return_citations", true)
            put("search_domain_filter", JSONArray().apply { put("perplexity.ai") }) // Use JSONArray
            put("return_images", false)
            put("return_related_questions", true)
        }
        
        return json.toString().toRequestBody("application/json".toMediaType())
    }
    
    /**
     * 🔍 Parse Perplexity search response
     */
    private fun parseSearchResponse(responseBody: String?): PerplexityResponse {
        return try {
            if (responseBody.isNullOrEmpty()) {
                return PerplexityResponse(
                    text = "I didn't receive a proper response from the search service.",
                    confidence = 0.2f,
                    sources = emptyList(),
                    relatedQuestions = emptyList(),
                    processingTime = 0L
                )
            }
            
            val json = JSONObject(responseBody)
            val choices = json.getJSONArray("choices")
            
            if (choices.length() > 0) {
                val firstChoice = choices.getJSONObject(0)
                val message = firstChoice.getJSONObject("message")
                val content = message.getString("content").trim()
                
                // Extract citations if available
                val sources = extractSources(json)
                
                // Extract related questions if available
                val relatedQuestions = extractRelatedQuestions(json)
                
                PerplexityResponse(
                    text = content,
                    confidence = 0.9f, // High confidence for real-time search
                    sources = sources,
                    relatedQuestions = relatedQuestions,
                    processingTime = 0L
                )
            } else {
                PerplexityResponse(
                    text = "No search results were found for your query.",
                    confidence = 0.3f,
                    sources = emptyList(),
                    relatedQuestions = emptyList(),
                    processingTime = 0L
                )
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error parsing Perplexity response", e)
            PerplexityResponse(
                text = "I had trouble processing the search results. Please try again.",
                confidence = 0.2f,
                sources = emptyList(),
                relatedQuestions = emptyList(),
                processingTime = 0L
            )
        }
    }
    
    /**
     * 📚 Extract sources from response
     */
    private fun extractSources(json: JSONObject): List<String> {
        return try {
            val sources = mutableListOf<String>()
            
            if (json.has("citations")) {
                val citations = json.getJSONArray("citations")
                for (i in 0 until citations.length()) {
                    val citation = citations.getJSONObject(i)
                    if (citation.has("url")) {
                        sources.add(citation.getString("url"))
                    }
                }
            }
            
            sources.take(3) // Limit to top 3 sources
        } catch (e: Exception) {
            Log.w(TAG, "Could not extract sources", e)
            emptyList()
        }
    }
    
    /**
     * ❓ Extract related questions
     */
    private fun extractRelatedQuestions(json: JSONObject): List<String> {
        return try {
            val questions = mutableListOf<String>()
            
            if (json.has("related_questions")) {
                val relatedQuestions = json.getJSONArray("related_questions")
                for (i in 0 until relatedQuestions.length()) {
                    questions.add(relatedQuestions.getString(i))
                }
            }
            
            questions.take(3) // Limit to top 3 questions
        } catch (e: Exception) {
            Log.w(TAG, "Could not extract related questions", e)
            emptyList()
        }
    }
    
    /**
     * 📰 Get latest news
     */
    suspend fun getLatestNews(topic: String): PerplexityResponse = withContext(Dispatchers.IO) {
        val newsQuery = "What are the latest news and updates about $topic today?"
        searchAndAnswer(newsQuery)
    }
    
    /**
     * 🌤️ Get weather information
     */
    suspend fun getWeatherInfo(location: String): PerplexityResponse = withContext(Dispatchers.IO) {
        val weatherQuery = "What is the current weather and forecast for $location?"
        searchAndAnswer(weatherQuery)
    }
    
    /**
     * 📈 Get stock information
     */
    suspend fun getStockInfo(symbol: String): PerplexityResponse = withContext(Dispatchers.IO) {
        val stockQuery = "What is the current stock price and recent performance of $symbol?"
        searchAndAnswer(stockQuery)
    }
    
    /**
     * 🔧 Test API connection
     */
    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        try {
            val testResponse = searchAndAnswer("What is the current date and time?")
            testResponse.confidence > 0.5f
        } catch (e: Exception) {
            Log.e(TAG, "❌ Connection test failed", e)
            false
        }
    }
    
    /**
     * 🎯 Quick fact lookup
     */
    suspend fun quickFact(query: String): PerplexityResponse = withContext(Dispatchers.IO) {
        val factQuery = "Provide a quick, accurate fact about: $query"
        searchAndAnswer(factQuery)
    }
}

/**
 * 📊 Perplexity Response Data
 */
data class PerplexityResponse(
    val text: String,
    val confidence: Float,
    val sources: List<String>,
    val relatedQuestions: List<String>,
    val processingTime: Long
)
