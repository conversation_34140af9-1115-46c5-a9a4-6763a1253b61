package com.zara.assistant.core;

/**
 * 🎯 Zara Constants - God Mode Configuration
 *
 * All constants organized for maximum performance and clarity
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\r\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u000b\u0003\u0004\u0005\u0006\u0007\b\t\n\u000b\f\rB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u000e"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants;", "", "()V", "AI", "Debug", "Localization", "Network", "Performance", "Personality", "Security", "System", "TTS", "UI", "Voice", "app_debug"})
public final class ZaraConstants {
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.core.ZaraConstants INSTANCE = null;
    
    private ZaraConstants() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants$AI;", "", "()V", "CONVERSATION_TIMEOUT_MS", "", "EMOTION_DETECTION_ENABLED", "", "MAX_CONVERSATION_HISTORY", "", "MAX_RESPONSE_LENGTH", "MULTIMODAL_ENABLED", "RESPONSE_CACHE_SIZE", "app_debug"})
    public static final class AI {
        public static final int MAX_RESPONSE_LENGTH = 500;
        public static final long CONVERSATION_TIMEOUT_MS = 300000L;
        public static final int MAX_CONVERSATION_HISTORY = 10;
        public static final int RESPONSE_CACHE_SIZE = 100;
        public static final boolean EMOTION_DETECTION_ENABLED = true;
        public static final boolean MULTIMODAL_ENABLED = true;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.ZaraConstants.AI INSTANCE = null;
        
        private AI() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants$Debug;", "", "()V", "ANALYTICS_ENABLED", "", "CRASH_REPORTING", "PERFORMANCE_MONITORING", "VERBOSE_LOGGING", "app_debug"})
    public static final class Debug {
        public static final boolean VERBOSE_LOGGING = true;
        public static final boolean PERFORMANCE_MONITORING = true;
        public static final boolean CRASH_REPORTING = true;
        public static final boolean ANALYTICS_ENABLED = true;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.ZaraConstants.Debug INSTANCE = null;
        
        private Debug() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00070\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\r"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants$Localization;", "", "()V", "AUTO_LANGUAGE_DETECTION", "", "CODE_SWITCHING_ENABLED", "DEFAULT_LANGUAGE", "", "HINDI_SUPPORT_ENABLED", "SUPPORTED_LANGUAGES", "", "getSUPPORTED_LANGUAGES", "()Ljava/util/List;", "app_debug"})
    public static final class Localization {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DEFAULT_LANGUAGE = "en";
        public static final boolean HINDI_SUPPORT_ENABLED = true;
        public static final boolean AUTO_LANGUAGE_DETECTION = true;
        public static final boolean CODE_SWITCHING_ENABLED = true;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.List<java.lang.String> SUPPORTED_LANGUAGES = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.ZaraConstants.Localization INSTANCE = null;
        
        private Localization() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getSUPPORTED_LANGUAGES() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants$Network;", "", "()V", "AZURE_SPEECH_REGION", "", "AZURE_STREAMING_ENABLED", "", "AZURE_STT_LANGUAGE", "CACHE_SIZE_MB", "", "CONNECTION_TIMEOUT_MS", "READ_TIMEOUT_MS", "RETRY_COUNT", "", "app_debug"})
    public static final class Network {
        public static final long CONNECTION_TIMEOUT_MS = 10000L;
        public static final long READ_TIMEOUT_MS = 30000L;
        public static final int RETRY_COUNT = 3;
        public static final long CACHE_SIZE_MB = 50L;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AZURE_SPEECH_REGION = "eastus";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AZURE_STT_LANGUAGE = "en-US";
        public static final boolean AZURE_STREAMING_ENABLED = true;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.ZaraConstants.Network INSTANCE = null;
        
        private Network() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants$Performance;", "", "()V", "BACKGROUND_PROCESSING_ENABLED", "", "DISK_CACHE_SIZE_MB", "", "EDGE_COMPUTING_ENABLED", "MEMORY_CACHE_SIZE_MB", "PREDICTIVE_CACHING_ENABLED", "app_debug"})
    public static final class Performance {
        public static final long MEMORY_CACHE_SIZE_MB = 100L;
        public static final long DISK_CACHE_SIZE_MB = 200L;
        public static final boolean BACKGROUND_PROCESSING_ENABLED = true;
        public static final boolean PREDICTIVE_CACHING_ENABLED = true;
        public static final boolean EDGE_COMPUTING_ENABLED = true;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.ZaraConstants.Performance INSTANCE = null;
        
        private Performance() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants$Personality;", "", "()V", "ADAPTIVE_PERSONALITY_ENABLED", "", "ANGER_THRESHOLD", "", "CONTEXTUAL_RESPONSES_ENABLED", "EMOTION_DETECTION_ENABLED", "EXCITEMENT_THRESHOLD", "HAPPINESS_THRESHOLD", "LEARNING_ENABLED", "SADNESS_THRESHOLD", "app_debug"})
    public static final class Personality {
        public static final boolean EMOTION_DETECTION_ENABLED = true;
        public static final boolean ADAPTIVE_PERSONALITY_ENABLED = true;
        public static final boolean CONTEXTUAL_RESPONSES_ENABLED = true;
        public static final boolean LEARNING_ENABLED = true;
        public static final float HAPPINESS_THRESHOLD = 0.7F;
        public static final float SADNESS_THRESHOLD = 0.6F;
        public static final float ANGER_THRESHOLD = 0.8F;
        public static final float EXCITEMENT_THRESHOLD = 0.75F;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.ZaraConstants.Personality INSTANCE = null;
        
        private Personality() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants$Security;", "", "()V", "CONVERSATION_ENCRYPTION_ENABLED", "", "LOCAL_PROCESSING_PREFERRED", "PRIVACY_MODE_AVAILABLE", "VOICE_BIOMETRICS_ENABLED", "app_debug"})
    public static final class Security {
        public static final boolean VOICE_BIOMETRICS_ENABLED = true;
        public static final boolean CONVERSATION_ENCRYPTION_ENABLED = true;
        public static final boolean LOCAL_PROCESSING_PREFERRED = true;
        public static final boolean PRIVACY_MODE_AVAILABLE = true;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.ZaraConstants.Security INSTANCE = null;
        
        private Security() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants$System;", "", "()V", "ACCESSIBILITY_SERVICE_ENABLED", "", "BATTERY_OPTIMIZATION_DISABLED", "NOTIFICATION_LISTENER_ENABLED", "SYSTEM_OVERLAY_ENABLED", "app_debug"})
    public static final class System {
        public static final boolean ACCESSIBILITY_SERVICE_ENABLED = true;
        public static final boolean NOTIFICATION_LISTENER_ENABLED = true;
        public static final boolean SYSTEM_OVERLAY_ENABLED = true;
        public static final boolean BATTERY_OPTIMIZATION_DISABLED = true;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.ZaraConstants.System INSTANCE = null;
        
        private System() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants$TTS;", "", "()V", "EMOTION_SYNTHESIS_ENABLED", "", "NEURAL_VOICE_ENABLED", "SPEECH_PITCH", "", "SPEECH_RATE", "STREAMING_ENABLED", "VOICE_FEMALE", "", "VOICE_HINDI_FEMALE", "VOICE_HINDI_MALE", "VOICE_MALE", "app_debug"})
    public static final class TTS {
        public static final float SPEECH_RATE = 1.0F;
        public static final float SPEECH_PITCH = 1.0F;
        public static final boolean STREAMING_ENABLED = true;
        public static final boolean NEURAL_VOICE_ENABLED = true;
        public static final boolean EMOTION_SYNTHESIS_ENABLED = true;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VOICE_FEMALE = "en-US-JennyNeural";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VOICE_MALE = "en-US-GuyNeural";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VOICE_HINDI_FEMALE = "hi-IN-SwaraNeural";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VOICE_HINDI_MALE = "hi-IN-MadhurNeural";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.ZaraConstants.TTS INSTANCE = null;
        
        private TTS() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants$UI;", "", "()V", "ANIMATION_DURATION_MS", "", "DARK_MODE_DEFAULT", "", "HAPTIC_FEEDBACK_ENABLED", "NEUMORPHISM_CORNER_RADIUS", "", "NEUMORPHISM_ELEVATION", "VOICE_WAVE_UPDATE_INTERVAL_MS", "app_debug"})
    public static final class UI {
        public static final long ANIMATION_DURATION_MS = 300L;
        public static final float NEUMORPHISM_ELEVATION = 8.0F;
        public static final float NEUMORPHISM_CORNER_RADIUS = 16.0F;
        public static final long VOICE_WAVE_UPDATE_INTERVAL_MS = 50L;
        public static final boolean HAPTIC_FEEDBACK_ENABLED = true;
        public static final boolean DARK_MODE_DEFAULT = true;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.ZaraConstants.UI INSTANCE = null;
        
        private UI() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/zara/assistant/core/ZaraConstants$Voice;", "", "()V", "AUTO_LISTEN_TIMEOUT_MS", "", "MAX_RECORDING_DURATION_MS", "SILENCE_TIMEOUT_MS", "VOICE_CONFIDENCE_THRESHOLD", "", "WAKE_WORD", "", "WAKE_WORD_SENSITIVITY", "app_debug"})
    public static final class Voice {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String WAKE_WORD = "hey zara";
        public static final float WAKE_WORD_SENSITIVITY = 0.7F;
        public static final long AUTO_LISTEN_TIMEOUT_MS = 30000L;
        public static final float VOICE_CONFIDENCE_THRESHOLD = 0.6F;
        public static final long MAX_RECORDING_DURATION_MS = 10000L;
        public static final long SILENCE_TIMEOUT_MS = 2000L;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.ZaraConstants.Voice INSTANCE = null;
        
        private Voice() {
            super();
        }
    }
}