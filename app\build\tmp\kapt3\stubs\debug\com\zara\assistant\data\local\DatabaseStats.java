package com.zara.assistant.data.local;

import androidx.room.*;
import androidx.sqlite.db.SupportSQLiteDatabase;
import com.zara.assistant.data.local.dao.ConversationDao;
import com.zara.assistant.data.local.dao.VoiceCommandDao;
import com.zara.assistant.data.local.entity.ConversationEntity;
import com.zara.assistant.data.local.entity.VoiceCommandEntity;
import com.zara.assistant.data.local.converter.DateConverter;
import com.zara.assistant.data.local.converter.EmotionConverter;

/**
 * 📊 Database Statistics
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0011\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B3\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0006H\u00c6\u0003J\u000f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u000bH\u00c6\u0003JA\u0010\u001b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00d6\u0001J\t\u0010 \u001a\u00020\tH\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014\u00a8\u0006!"}, d2 = {"Lcom/zara/assistant/data/local/DatabaseStats;", "", "totalConversations", "", "totalVoiceCommands", "averageConfidence", "", "mostUsedCommands", "", "", "databaseSize", "", "(IIFLjava/util/List;J)V", "getAverageConfidence", "()F", "getDatabaseSize", "()J", "getMostUsedCommands", "()Ljava/util/List;", "getTotalConversations", "()I", "getTotalVoiceCommands", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class DatabaseStats {
    private final int totalConversations = 0;
    private final int totalVoiceCommands = 0;
    private final float averageConfidence = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> mostUsedCommands = null;
    private final long databaseSize = 0L;
    
    public DatabaseStats(int totalConversations, int totalVoiceCommands, float averageConfidence, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> mostUsedCommands, long databaseSize) {
        super();
    }
    
    public final int getTotalConversations() {
        return 0;
    }
    
    public final int getTotalVoiceCommands() {
        return 0;
    }
    
    public final float getAverageConfidence() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getMostUsedCommands() {
        return null;
    }
    
    public final long getDatabaseSize() {
        return 0L;
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component4() {
        return null;
    }
    
    public final long component5() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.local.DatabaseStats copy(int totalConversations, int totalVoiceCommands, float averageConfidence, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> mostUsedCommands, long databaseSize) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}