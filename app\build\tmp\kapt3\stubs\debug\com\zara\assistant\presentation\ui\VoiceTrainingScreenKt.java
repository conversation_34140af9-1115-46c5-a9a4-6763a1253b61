package com.zara.assistant.presentation.ui;

import androidx.compose.foundation.layout.*;
import androidx.compose.material.icons.Icons;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.text.font.FontWeight;
import com.zara.assistant.R;
import com.zara.assistant.presentation.viewmodel.ZaraViewModel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000$\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\u001a.\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0003\u001a \u0010\t\u001a\u00020\u00012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u00a8\u0006\r"}, d2 = {"TrainingExerciseItem", "", "title", "", "description", "isCompleted", "", "onStartTraining", "Lkotlin/Function0;", "VoiceTrainingScreen", "onNavigateBack", "viewModel", "Lcom/zara/assistant/presentation/viewmodel/ZaraViewModel;", "app_debug"})
public final class VoiceTrainingScreenKt {
    
    /**
     * 🎙️ Voice Training Screen - God Mode Implementation
     *
     * Features:
     * - Voice pattern training
     * - Accent adaptation
     * - Pronunciation improvement
     * - Real-time feedback
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void VoiceTrainingScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.presentation.viewmodel.ZaraViewModel viewModel) {
    }
    
    /**
     * 🎯 Training Exercise Item
     */
    @androidx.compose.runtime.Composable()
    private static final void TrainingExerciseItem(java.lang.String title, java.lang.String description, boolean isCompleted, kotlin.jvm.functions.Function0<kotlin.Unit> onStartTraining) {
    }
}