package com.zara.assistant.presentation.ui;

import androidx.compose.animation.*;
import androidx.compose.animation.core.*;
import androidx.compose.foundation.*;
import androidx.compose.foundation.layout.*;
import androidx.compose.material.icons.Icons;
import androidx.compose.material.icons.filled.*;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.graphics.Brush;
import androidx.compose.ui.graphics.vector.ImageVector;
import androidx.compose.ui.text.font.FontWeight;
import androidx.compose.ui.text.style.TextAlign;
import com.zara.assistant.domain.model.*;
import com.zara.assistant.presentation.theme.*;
import com.zara.assistant.presentation.ui.components.*;
import com.zara.assistant.presentation.viewmodel.ZaraViewModel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000F\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0016\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001aR\u0010\u0004\u001a\u00020\u00012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a<\u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\f2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0006\u0010\u000f\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0003\u001aT\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\f2\u0006\u0010\u0016\u001a\u00020\f2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0006\u0010\u000b\u001a\u00020\fH\u0003\u001a\u0015\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u0013\u001a\u00020\u0014H\u0003\u00a2\u0006\u0002\u0010\u001f\u00a8\u0006 "}, d2 = {"PermissionRequiredCard", "", "onRequestPermissions", "Lkotlin/Function0;", "ZaraMainScreen", "onNavigateToSettings", "onNavigateToConversation", "onNavigateToSystemControls", "viewModel", "Lcom/zara/assistant/presentation/viewmodel/ZaraViewModel;", "ZaraTopBar", "isDarkMode", "", "onToggleDarkMode", "onShowSettings", "wakeWordActive", "responseTime", "", "ZaraVoiceInterface", "voiceState", "Lcom/zara/assistant/domain/model/VoiceState;", "isListening", "isSpeaking", "currentEmotion", "Lcom/zara/assistant/domain/model/Emotion;", "voiceWaveScale", "", "onStartVoice", "onStopVoice", "getVoiceStateColor", "Landroidx/compose/ui/graphics/Color;", "(Lcom/zara/assistant/domain/model/VoiceState;)J", "app_debug"})
public final class ZaraMainScreenKt {
    
    /**
     * 🏠 Zara Main Screen - God Mode Neumorphism UI
     *
     * Features:
     * - Beautiful neumorphism design
     * - Real-time voice visualization
     * - Smooth animations
     * - Responsive layout
     * - Accessibility optimized
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ZaraMainScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToConversation, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSystemControls, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRequestPermissions, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.presentation.viewmodel.ZaraViewModel viewModel) {
    }
    
    /**
     * 📱 Top Bar Component
     */
    @androidx.compose.runtime.Composable()
    private static final void ZaraTopBar(boolean isDarkMode, kotlin.jvm.functions.Function0<kotlin.Unit> onToggleDarkMode, kotlin.jvm.functions.Function0<kotlin.Unit> onShowSettings, boolean wakeWordActive, long responseTime) {
    }
    
    /**
     * 🔐 Permission Required Card
     */
    @androidx.compose.runtime.Composable()
    private static final void PermissionRequiredCard(kotlin.jvm.functions.Function0<kotlin.Unit> onRequestPermissions) {
    }
    
    /**
     * 🎙️ Voice Interface Component
     */
    @androidx.compose.runtime.Composable()
    private static final void ZaraVoiceInterface(com.zara.assistant.domain.model.VoiceState voiceState, boolean isListening, boolean isSpeaking, com.zara.assistant.domain.model.Emotion currentEmotion, float voiceWaveScale, kotlin.jvm.functions.Function0<kotlin.Unit> onStartVoice, kotlin.jvm.functions.Function0<kotlin.Unit> onStopVoice, boolean isDarkMode) {
    }
    
    /**
     * 🎨 Get voice state color
     */
    @androidx.compose.runtime.Composable()
    private static final long getVoiceStateColor(com.zara.assistant.domain.model.VoiceState voiceState) {
        return 0L;
    }
}