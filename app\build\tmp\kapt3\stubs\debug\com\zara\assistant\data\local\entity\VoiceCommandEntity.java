package com.zara.assistant.data.local.entity;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import com.zara.assistant.domain.model.Emotion;
import java.util.Date;

/**
 * 🎙️ Voice Command Entity - God Mode Database Model
 *
 * Features:
 * - Room entity for voice command analytics
 * - Performance tracking
 * - Success rate monitoring
 * - Emotion analysis
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b.\b\u0087\b\u0018\u00002\u00020\u0001B\u0089\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\u0003\u0012\u0006\u0010\f\u001a\u00020\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0005\u0012\u0006\u0010\u000f\u001a\u00020\r\u0012\u0006\u0010\u0010\u001a\u00020\b\u0012\u0006\u0010\u0011\u001a\u00020\b\u0012\u0006\u0010\u0012\u001a\u00020\u0013\u0012\u0006\u0010\u0014\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0017\u00a2\u0006\u0002\u0010\u0018J\t\u00100\u001a\u00020\u0003H\u00c6\u0003J\t\u00101\u001a\u00020\bH\u00c6\u0003J\t\u00102\u001a\u00020\bH\u00c6\u0003J\t\u00103\u001a\u00020\u0013H\u00c6\u0003J\t\u00104\u001a\u00020\u0005H\u00c6\u0003J\t\u00105\u001a\u00020\u0005H\u00c6\u0003J\u0010\u00106\u001a\u0004\u0018\u00010\u0017H\u00c6\u0003\u00a2\u0006\u0002\u0010-J\t\u00107\u001a\u00020\u0005H\u00c6\u0003J\t\u00108\u001a\u00020\u0005H\u00c6\u0003J\t\u00109\u001a\u00020\bH\u00c6\u0003J\t\u0010:\u001a\u00020\nH\u00c6\u0003J\t\u0010;\u001a\u00020\u0003H\u00c6\u0003J\t\u0010<\u001a\u00020\rH\u00c6\u0003J\u000b\u0010=\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010>\u001a\u00020\rH\u00c6\u0003J\u00a8\u0001\u0010?\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u000f\u001a\u00020\r2\b\b\u0002\u0010\u0010\u001a\u00020\b2\b\b\u0002\u0010\u0011\u001a\u00020\b2\b\b\u0002\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u00052\b\b\u0002\u0010\u0015\u001a\u00020\u00052\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0017H\u00c6\u0001\u00a2\u0006\u0002\u0010@J\u0013\u0010A\u001a\u00020\r2\b\u0010B\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010C\u001a\u00020\u0017H\u00d6\u0001J\t\u0010D\u001a\u00020\u0005H\u00d6\u0001R\u0016\u0010\u0010\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0016\u0010\u0011\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001aR\u0016\u0010\u0004\u001a\u00020\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0016\u0010\u0006\u001a\u00020\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001dR\u0016\u0010\u0007\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001aR\u0016\u0010\u0012\u001a\u00020\u00138\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0016\u0010\t\u001a\u00020\n8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u0018\u0010\u000e\u001a\u0004\u0018\u00010\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u001dR\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0016\u0010\u0015\u001a\u00020\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001dR\u0016\u0010\u000b\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010&R\u0016\u0010\u0014\u001a\u00020\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u001dR\u0016\u0010\f\u001a\u00020\r8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u001a\u0010\u0016\u001a\u0004\u0018\u00010\u00178\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010.\u001a\u0004\b,\u0010-R\u0016\u0010\u000f\u001a\u00020\r8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010+\u00a8\u0006E"}, d2 = {"Lcom/zara/assistant/data/local/entity/VoiceCommandEntity;", "", "id", "", "commandText", "", "commandType", "confidenceScore", "", "emotion", "Lcom/zara/assistant/domain/model/Emotion;", "processingTimeMs", "success", "", "errorMessage", "wakeWordDetected", "audioQualityScore", "backgroundNoiseLevel", "createdAt", "Ljava/util/Date;", "sessionId", "language", "userFeedback", "", "(JLjava/lang/String;Ljava/lang/String;FLcom/zara/assistant/domain/model/Emotion;JZLjava/lang/String;ZFFLjava/util/Date;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)V", "getAudioQualityScore", "()F", "getBackgroundNoiseLevel", "getCommandText", "()Ljava/lang/String;", "getCommandType", "getConfidenceScore", "getCreatedAt", "()Ljava/util/Date;", "getEmotion", "()Lcom/zara/assistant/domain/model/Emotion;", "getErrorMessage", "getId", "()J", "getLanguage", "getProcessingTimeMs", "getSessionId", "getSuccess", "()Z", "getUserFeedback", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getWakeWordDetected", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(JLjava/lang/String;Ljava/lang/String;FLcom/zara/assistant/domain/model/Emotion;JZLjava/lang/String;ZFFLjava/util/Date;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Lcom/zara/assistant/data/local/entity/VoiceCommandEntity;", "equals", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "voice_commands")
public final class VoiceCommandEntity {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @androidx.room.ColumnInfo(name = "command_text")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String commandText = null;
    @androidx.room.ColumnInfo(name = "command_type")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String commandType = null;
    @androidx.room.ColumnInfo(name = "confidence_score")
    private final float confidenceScore = 0.0F;
    @androidx.room.ColumnInfo(name = "emotion")
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.Emotion emotion = null;
    @androidx.room.ColumnInfo(name = "processing_time_ms")
    private final long processingTimeMs = 0L;
    @androidx.room.ColumnInfo(name = "success")
    private final boolean success = false;
    @androidx.room.ColumnInfo(name = "error_message")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    @androidx.room.ColumnInfo(name = "wake_word_detected")
    private final boolean wakeWordDetected = false;
    @androidx.room.ColumnInfo(name = "audio_quality_score")
    private final float audioQualityScore = 0.0F;
    @androidx.room.ColumnInfo(name = "background_noise_level")
    private final float backgroundNoiseLevel = 0.0F;
    @androidx.room.ColumnInfo(name = "created_at")
    @org.jetbrains.annotations.NotNull()
    private final java.util.Date createdAt = null;
    @androidx.room.ColumnInfo(name = "session_id")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String sessionId = null;
    @androidx.room.ColumnInfo(name = "language")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String language = null;
    @androidx.room.ColumnInfo(name = "user_feedback")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer userFeedback = null;
    
    public VoiceCommandEntity(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String commandText, @org.jetbrains.annotations.NotNull()
    java.lang.String commandType, float confidenceScore, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Emotion emotion, long processingTimeMs, boolean success, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, boolean wakeWordDetected, float audioQualityScore, float backgroundNoiseLevel, @org.jetbrains.annotations.NotNull()
    java.util.Date createdAt, @org.jetbrains.annotations.NotNull()
    java.lang.String sessionId, @org.jetbrains.annotations.NotNull()
    java.lang.String language, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userFeedback) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCommandText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCommandType() {
        return null;
    }
    
    public final float getConfidenceScore() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.Emotion getEmotion() {
        return null;
    }
    
    public final long getProcessingTimeMs() {
        return 0L;
    }
    
    public final boolean getSuccess() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    public final boolean getWakeWordDetected() {
        return false;
    }
    
    public final float getAudioQualityScore() {
        return 0.0F;
    }
    
    public final float getBackgroundNoiseLevel() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date getCreatedAt() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSessionId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLanguage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getUserFeedback() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final float component10() {
        return 0.0F;
    }
    
    public final float component11() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component15() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.Emotion component5() {
        return null;
    }
    
    public final long component6() {
        return 0L;
    }
    
    public final boolean component7() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.local.entity.VoiceCommandEntity copy(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String commandText, @org.jetbrains.annotations.NotNull()
    java.lang.String commandType, float confidenceScore, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Emotion emotion, long processingTimeMs, boolean success, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, boolean wakeWordDetected, float audioQualityScore, float backgroundNoiseLevel, @org.jetbrains.annotations.NotNull()
    java.util.Date createdAt, @org.jetbrains.annotations.NotNull()
    java.lang.String sessionId, @org.jetbrains.annotations.NotNull()
    java.lang.String language, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userFeedback) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}