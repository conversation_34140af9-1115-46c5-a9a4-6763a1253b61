package com.zara.assistant.domain.model;

import java.util.*;

/**
 * 🤖 AI Response Model
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001c\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bg\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\f\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\u0002\u0010\u0013J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\t\u0010%\u001a\u00020\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\'\u001a\u00020\bH\u00c6\u0003J\t\u0010(\u001a\u00020\nH\u00c6\u0003J\u000f\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003J\u000f\u0010*\u001a\b\u0012\u0004\u0012\u00020\u000e0\fH\u00c6\u0003J\u000b\u0010+\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003J\t\u0010,\u001a\u00020\u0012H\u00c6\u0003Jq\u0010-\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\f2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u00c6\u0001J\u0013\u0010.\u001a\u00020/2\b\u00100\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00101\u001a\u000202H\u00d6\u0001J\t\u00103\u001a\u00020\u0003H\u00d6\u0001R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001bR\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#\u00a8\u00064"}, d2 = {"Lcom/zara/assistant/domain/model/AIResponse;", "", "id", "", "text", "type", "Lcom/zara/assistant/domain/model/ResponseType;", "confidence", "", "emotion", "Lcom/zara/assistant/domain/model/Emotion;", "suggestions", "", "actions", "Lcom/zara/assistant/domain/model/SystemAction;", "metadata", "Lcom/zara/assistant/domain/model/ResponseMetadata;", "timestamp", "Ljava/util/Date;", "(Ljava/lang/String;Ljava/lang/String;Lcom/zara/assistant/domain/model/ResponseType;FLcom/zara/assistant/domain/model/Emotion;Ljava/util/List;Ljava/util/List;Lcom/zara/assistant/domain/model/ResponseMetadata;Ljava/util/Date;)V", "getActions", "()Ljava/util/List;", "getConfidence", "()F", "getEmotion", "()Lcom/zara/assistant/domain/model/Emotion;", "getId", "()Ljava/lang/String;", "getMetadata", "()Lcom/zara/assistant/domain/model/ResponseMetadata;", "getSuggestions", "getText", "getTimestamp", "()Ljava/util/Date;", "getType", "()Lcom/zara/assistant/domain/model/ResponseType;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class AIResponse {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String text = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.ResponseType type = null;
    private final float confidence = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.Emotion emotion = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> suggestions = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.zara.assistant.domain.model.SystemAction> actions = null;
    @org.jetbrains.annotations.Nullable()
    private final com.zara.assistant.domain.model.ResponseMetadata metadata = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Date timestamp = null;
    
    public AIResponse(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ResponseType type, float confidence, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Emotion emotion, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> suggestions, @org.jetbrains.annotations.NotNull()
    java.util.List<com.zara.assistant.domain.model.SystemAction> actions, @org.jetbrains.annotations.Nullable()
    com.zara.assistant.domain.model.ResponseMetadata metadata, @org.jetbrains.annotations.NotNull()
    java.util.Date timestamp) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ResponseType getType() {
        return null;
    }
    
    public final float getConfidence() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.Emotion getEmotion() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getSuggestions() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.zara.assistant.domain.model.SystemAction> getActions() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.zara.assistant.domain.model.ResponseMetadata getMetadata() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date getTimestamp() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ResponseType component3() {
        return null;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.Emotion component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.zara.assistant.domain.model.SystemAction> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.zara.assistant.domain.model.ResponseMetadata component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.AIResponse copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ResponseType type, float confidence, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.Emotion emotion, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> suggestions, @org.jetbrains.annotations.NotNull()
    java.util.List<com.zara.assistant.domain.model.SystemAction> actions, @org.jetbrains.annotations.Nullable()
    com.zara.assistant.domain.model.ResponseMetadata metadata, @org.jetbrains.annotations.NotNull()
    java.util.Date timestamp) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}